<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Checkout\Model\DefaultConfigProvider">
		<plugin name="add_shipping_data_to_checkout_config" type="Coditron\CustomShippingRate\Plugin\AddShippingDataToCheckoutConfig"/>
	</type>
	<type name="Magento\Checkout\Model\ShippingInformationManagement">
		<plugin name="update_shipping_data_on_method_selection" type="Coditron\CustomShippingRate\Plugin\UpdateShippingDataOnMethodSelection" />
	</type>
	<type name="Magento\OfflineShipping\Model\Carrier\Freeshipping">
		<plugin name="enable_freeshipping_with_thresholds" type="Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier\FreeShippingPlugin" />
	</type>
	<preference for="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" type="Coditron\CustomShippingRate\Model\CustomShippingInformationManagement" />
	<preference for="Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface"
                type="Coditron\CustomShippingRate\Model\Data\CustomShippingInformation" />
    <preference for="Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface" type="Coditron\CustomShippingRate\Model\ShipTableRatesRepository"/>
	<preference for="Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface" type="Coditron\CustomShippingRate\Model\ShipTableRates"/>
	<preference for="Coditron\CustomShippingRate\Api\Data\ShipTableRatesSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<preference for="Coditron\CustomShippingRate\Api\PerSellerShippingManagementInterface" type="Coditron\CustomShippingRate\Model\PerSellerShippingManagement"/>
	<preference for="Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface" type="Coditron\CustomShippingRate\Model\Data\PerSellerShippingMethod"/>
	<virtualType name="Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">coditron_customshippingrate_shiptablerates</argument>
			<argument name="resourceModel" xsi:type="string">Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="coditron_customshippingrate_shiptablerates_listing_data_source" xsi:type="string">Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Grid\Collection</item>
			</argument>
		</arguments>
	</type>

    <!-- Example of adding a custom rate provider -->
<!--    <type name="Coditron\CustomShippingRate\Model\RateProviders\RatePoolProvider">-->
<!--        <arguments>-->
<!--            <argument xsi:type="array" name="providers">-->
<!--                <item name="shopify" xsi:type="object">Coditron\CustomShippingRate\Model\RateProviders\RatesByServiceFactory</item>-->
<!--            </argument>-->
<!--        </arguments>-->
<!--    </type>-->

    <!-- Per-Seller Shipping Services -->
    <type name="Coditron\CustomShippingRate\Service\PerSellerShippingRateCollector">
        <arguments>
            <argument name="productDataBuilder" xsi:type="object">Coditron\CustomShippingRate\Model\Command\ProductDataBuilder</argument>
            <argument name="ratePoolProvider" xsi:type="object">Coditron\CustomShippingRate\Model\RateProviders\RatePoolProvider</argument>
            <argument name="rateFactory" xsi:type="object">Magento\Shipping\Model\Rate\ResultFactory</argument>
            <argument name="rateMethodFactory" xsi:type="object">Magento\Quote\Model\Quote\Address\RateResult\MethodFactory</argument>
            <argument name="freeShippingService" xsi:type="object">Coditron\CustomShippingRate\Service\FreeShippingService</argument>
            <argument name="sellerFactory" xsi:type="object">Webkul\Marketplace\Model\SellerFactory</argument>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>

    <!-- Console Commands -->
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="coditron_debug_freeshipping" xsi:type="object">Coditron\CustomShippingRate\Console\Command\DebugFreeShipping</item>
                <item name="coditron_test_freeshipping_carrier" xsi:type="object">Coditron\CustomShippingRate\Console\Command\TestFreeShippingCarrier</item>
                <item name="coditron_test_per_seller_shipping" xsi:type="object">Coditron\CustomShippingRate\Console\Command\TestPerSellerShipping</item>
            </argument>
        </arguments>
    </type>
</config>
