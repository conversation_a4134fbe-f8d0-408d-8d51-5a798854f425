<?xml version="1.0"?>
<!--
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="coditron" sortOrder="400" translate="label">
            <label>Coditron</label>
        </tab>
        <section id="coditron_customshippingrate" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="5" translate="label">
            <label>Custom Shipping Rate</label>
            <tab>coditron</tab>
            <resource>Coditron_CustomShippingRate::config_coditron_customshippingrate</resource>
            <group id="about_coditron" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="0" translate="label">
                <label>Coditron Custom Shipping Rate</label>
                <attribute type="expanded">1</attribute>

                <field id="active" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Enabled Seller Table Rates</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="link" translate="label" type="label" sortOrder="3" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Configuration</label>
                    <frontend_model>Coditron\CustomShippingRate\Block\Adminhtml\System\Config\Form\Field\Link</frontend_model>
                </field>
            </group>
        </section>
        <section id="carriers">
            <group id="customshippingrate" translate="label" type="text" sortOrder="150" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Coditron Custom Shipping Rate</label>
                <field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_on_frontend" translate="label" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Show On Frontend Checkout</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="per_seller_display" translate="label comment" type="select" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Per-Seller Shipping Display</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Show shipping options separately for each seller (e.g., "Seller A: Free Shipping", "Seller B: €5 Standard")</comment>
                </field>
                <field id="title" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Title</label>
                </field>
                <field id="shipping_type" translate="label comment" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Service Types</label>
                    <frontend_model>Coditron\CustomShippingRate\Block\Adminhtml\System\Config\Form\Field\ShippingList</frontend_model>
                    <backend_model>Coditron\CustomShippingRate\Config\Backend\Serialized\ArraySerialized</backend_model>
                    <comment>Service types (express, standard, etc.)</comment>
                </field>
                <field id="specificerrmsg" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Displayed Error Message</label>
                </field>
                <field id="showmethod" translate="label" type="select" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Show Method if Not Applicable</label>
                    <frontend_class>shipping-skip-hide</frontend_class>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="sallowspecific" translate="label" type="select" sortOrder="1900" showInDefault="1" showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Ship to Applicable Countries</label>
                    <frontend_class>shipping-applicable-country</frontend_class>
                    <source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
                </field>
                <field id="specificcountry" translate="label" type="multiselect" sortOrder="1910" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Ship to Specific Countries</label>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                    <can_be_empty>1</can_be_empty>
                </field>
                <field id="sort_order" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sort Order</label>
                </field>
            </group>
        </section>
    </system>
</config>
