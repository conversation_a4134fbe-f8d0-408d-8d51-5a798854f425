<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Coditron\CustomShippingRate\Model\Carrier;
use Magento\Quote\Model\Quote\Address\RateRequestFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Psr\Log\LoggerInterface;

class DebugPerSellerRates extends Command
{
    public function __construct(
        private readonly Carrier $customCarrier,
        private readonly RateRequestFactory $rateRequestFactory,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('coditron:debug-per-seller-rates')
            ->setDescription('Debug per-seller shipping rates')
            ->addOption(
                'country',
                'c',
                InputOption::VALUE_OPTIONAL,
                'Country code',
                'US'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Debugging Per-Seller Shipping Rates</info>');
        $output->writeln('=====================================');

        $country = $input->getOption('country');

        // Check configuration
        $perSellerEnabled = $this->scopeConfig->getValue(
            'carriers/customshippingrate/per_seller_display',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        
        $carrierActive = $this->scopeConfig->getValue(
            'carriers/customshippingrate/active',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        $output->writeln("Configuration:");
        $output->writeln("- Custom Carrier Active: " . ($carrierActive ? 'Yes' : 'No'));
        $output->writeln("- Per-Seller Display: " . ($perSellerEnabled ? 'Yes' : 'No'));
        $output->writeln("");

        if (!$carrierActive) {
            $output->writeln('<error>Custom shipping carrier is not active!</error>');
            $output->writeln('Enable it in: Admin → Stores → Configuration → Sales → Shipping Methods → Coditron Custom Shipping Rate');
            return Command::FAILURE;
        }

        if (!$perSellerEnabled) {
            $output->writeln('<error>Per-seller display is not enabled!</error>');
            $output->writeln('Enable it in: Admin → Stores → Configuration → Sales → Shipping Methods → Coditron Custom Shipping Rate → Per-Seller Shipping Display');
            return Command::FAILURE;
        }

        // Create a mock rate request
        $rateRequest = $this->rateRequestFactory->create();
        $rateRequest->setDestCountryId($country);
        $rateRequest->setBaseSubtotalInclTax(100.00);
        $rateRequest->setPackageWeight(2.0);
        $rateRequest->setAllItems([]);

        $output->writeln("Testing with mock rate request:");
        $output->writeln("- Country: {$country}");
        $output->writeln("- Subtotal: $100.00");
        $output->writeln("- Weight: 2.0kg");
        $output->writeln("");

        try {
            // Test if carrier can collect rates
            if (!$this->customCarrier->canCollectRates()) {
                $output->writeln('<error>Carrier cannot collect rates!</error>');
                return Command::FAILURE;
            }

            $output->writeln('<info>Carrier can collect rates</info>');

            // Get allowed methods
            $allowedMethods = $this->customCarrier->getAllowedMethods();
            $output->writeln("Allowed methods: " . implode(', ', array_keys($allowedMethods)));
            $output->writeln("");

            // Try to collect rates
            $output->writeln("Attempting to collect rates...");
            $result = $this->customCarrier->collectRates($rateRequest);

            if ($result === false) {
                $output->writeln('<error>Rate collection returned false</error>');
                return Command::FAILURE;
            }

            if (!$result || !$result->getAllRates()) {
                $output->writeln('<warning>No rates returned</warning>');
                $output->writeln('This might be because:');
                $output->writeln('1. No quote items with seller data');
                $output->writeln('2. No shipping table rates configured');
                $output->writeln('3. No sellers match the criteria');
                return Command::SUCCESS;
            }

            $rates = $result->getAllRates();
            $output->writeln("<info>Found " . count($rates) . " shipping rates:</info>");
            $output->writeln("");

            $freeRates = [];
            $standardRates = [];

            foreach ($rates as $rate) {
                $rateInfo = [
                    'Method Code' => $rate->getMethod(),
                    'Title' => $rate->getMethodTitle(),
                    'Price' => '€' . number_format($rate->getPrice(), 2),
                    'Carrier' => $rate->getCarrier(),
                    'Seller ID' => $rate->getData('seller_id') ?: 'N/A',
                    'Seller Name' => $rate->getData('seller_name') ?: 'N/A',
                    'Is Free' => $rate->getData('is_free_option') ? 'Yes' : 'No'
                ];

                if ($rate->getData('is_free_option')) {
                    $freeRates[] = $rateInfo;
                } else {
                    $standardRates[] = $rateInfo;
                }
            }

            if (!empty($freeRates)) {
                $output->writeln('<info>FREE SHIPPING RATES:</info>');
                foreach ($freeRates as $rate) {
                    $output->writeln("- {$rate['Title']} ({$rate['Method Code']})");
                    $output->writeln("  Seller: {$rate['Seller Name']} (ID: {$rate['Seller ID']})");
                    $output->writeln("  Price: {$rate['Price']}");
                    $output->writeln("");
                }
            }

            if (!empty($standardRates)) {
                $output->writeln('<info>STANDARD SHIPPING RATES:</info>');
                foreach ($standardRates as $rate) {
                    $output->writeln("- {$rate['Title']} ({$rate['Method Code']})");
                    $output->writeln("  Seller: {$rate['Seller Name']} (ID: {$rate['Seller ID']})");
                    $output->writeln("  Price: {$rate['Price']}");
                    $output->writeln("");
                }
            }

            $output->writeln('<info>Debug completed successfully!</info>');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $output->writeln('<error>Error during rate collection: ' . $e->getMessage() . '</error>');
            $this->logger->error('[DebugPerSellerRates] Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }
}
