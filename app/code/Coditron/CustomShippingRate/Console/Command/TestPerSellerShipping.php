<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Coditron\CustomShippingRate\Service\FreeShippingService;
use Coditron\CustomShippingRate\Service\PerSellerShippingRateCollector;
use Magento\Quote\Model\QuoteFactory;
use Magento\Quote\Model\Quote\Address\RateRequestFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class TestPerSellerShipping extends Command
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService,
        private readonly PerSellerShippingRateCollector $perSellerRateCollector,
        private readonly QuoteFactory $quoteFactory,
        private readonly RateRequestFactory $rateRequestFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct();
    }

    /**
     * Configure command
     */
    protected function configure(): void
    {
        $this->setName('coditron:test-per-seller-shipping')
            ->setDescription('Test per-seller shipping implementation')
            ->addOption(
                'quote-id',
                'q',
                InputOption::VALUE_OPTIONAL,
                'Quote ID to test with'
            )
            ->addOption(
                'country',
                'c',
                InputOption::VALUE_OPTIONAL,
                'Destination country code',
                'US'
            );
    }

    /**
     * Execute command
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Testing Per-Seller Shipping Implementation</info>');
        $output->writeln('==========================================');

        $quoteId = $input->getOption('quote-id');
        $country = $input->getOption('country');

        try {
            // Test 1: Free Shipping Service Analysis
            $output->writeln('<comment>Test 1: Free Shipping Service Analysis</comment>');
            $this->testFreeShippingService($output, $country);

            // Test 2: Per-Seller Rate Collection
            $output->writeln('<comment>Test 2: Per-Seller Rate Collection</comment>');
            if ($quoteId) {
                $this->testPerSellerRateCollection($output, (int)$quoteId, $country);
            } else {
                $output->writeln('<warning>No quote ID provided, skipping rate collection test</warning>');
            }

            // Test 3: Configuration Validation
            $output->writeln('<comment>Test 3: Configuration Validation</comment>');
            $this->testConfiguration($output);

            $output->writeln('<info>All tests completed successfully!</info>');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $output->writeln('<error>Test failed: ' . $e->getMessage() . '</error>');
            $this->logger->error('[TestPerSellerShipping] Test execution failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Test free shipping service functionality
     */
    private function testFreeShippingService(OutputInterface $output, string $country): void
    {
        $output->writeln("Testing free shipping service for country: {$country}");

        // Test threshold existence check
        $hasThresholds = $this->freeShippingService->hasThresholdsForCountry($country);
        $output->writeln("Has thresholds for {$country}: " . ($hasThresholds ? 'Yes' : 'No'));

        // Test threshold retrieval for different subtotals
        $testSubtotals = [50, 100, 200, 500];
        
        foreach ($testSubtotals as $subtotal) {
            $metThresholds = $this->freeShippingService->getMetThresholdsForCountry($country, $subtotal);
            $output->writeln("Subtotal ${subtotal}: " . count($metThresholds) . " thresholds met");
            
            foreach ($metThresholds as $threshold) {
                $output->writeln("  - Seller {$threshold->getSellerId()}: Min ${$threshold->getMinOrderAmount()}");
            }
        }

        // Test per-seller threshold checking
        $output->writeln("Testing per-seller threshold checking...");
        $sellerIds = [1, 2, 3]; // Test with common seller IDs
        
        foreach ($sellerIds as $sellerId) {
            $hasSellerThresholds = $this->freeShippingService->hasThresholdsForSeller($country, $sellerId);
            $output->writeln("Seller {$sellerId} has thresholds: " . ($hasSellerThresholds ? 'Yes' : 'No'));
            
            if ($hasSellerThresholds) {
                foreach ($testSubtotals as $subtotal) {
                    $metThresholds = $this->freeShippingService->getMetThresholdsForSeller($country, $subtotal, $sellerId);
                    if (!empty($metThresholds)) {
                        $output->writeln("  - Subtotal ${subtotal}: Qualifies for free shipping");
                        break;
                    }
                }
            }
        }

        $output->writeln('');
    }

    /**
     * Test per-seller rate collection
     */
    private function testPerSellerRateCollection(OutputInterface $output, int $quoteId, string $country): void
    {
        $output->writeln("Testing per-seller rate collection for quote: {$quoteId}");

        try {
            $quote = $this->quoteFactory->create()->load($quoteId);
            
            if (!$quote->getId()) {
                $output->writeln('<warning>Quote not found, creating mock rate request</warning>');
                $this->testMockRateCollection($output, $country);
                return;
            }

            $items = $quote->getAllVisibleItems();
            $output->writeln("Quote has " . count($items) . " items");

            // Analyze quote items by seller
            $sellerData = $this->freeShippingService->analyzeQuoteItemsBySeller($items);
            $output->writeln("Found " . count($sellerData) . " sellers in quote:");
            
            foreach ($sellerData as $sellerId => $data) {
                $output->writeln("  - Seller {$sellerId}: Subtotal ${$data['subtotal']}, Items: " . count($data['items']));
            }

            // Test per-seller free shipping evaluation
            $perSellerResults = $this->freeShippingService->evaluatePerSellerFreeShipping($sellerData, $country);
            $output->writeln("Per-seller free shipping evaluation:");
            
            foreach ($perSellerResults as $sellerId => $result) {
                $qualifies = $result['qualifies_for_free_shipping'] ? 'Yes' : 'No';
                $output->writeln("  - Seller {$sellerId}: Qualifies: {$qualifies}, Thresholds: " . count($result['met_thresholds']));
            }

            $output->writeln('');

        } catch (\Exception $e) {
            $output->writeln('<error>Error testing rate collection: ' . $e->getMessage() . '</error>');
        }
    }

    /**
     * Test with mock rate request
     */
    private function testMockRateCollection(OutputInterface $output, string $country): void
    {
        $output->writeln("Creating mock rate request for testing...");
        
        $rateRequest = $this->rateRequestFactory->create();
        $rateRequest->setDestCountryId($country);
        $rateRequest->setBaseSubtotalInclTax(150.00);
        $rateRequest->setPackageWeight(2.5);
        
        $output->writeln("Mock rate request created:");
        $output->writeln("  - Country: {$country}");
        $output->writeln("  - Subtotal: $150.00");
        $output->writeln("  - Weight: 2.5kg");
        $output->writeln('');
    }

    /**
     * Test configuration validation
     */
    private function testConfiguration(OutputInterface $output): void
    {
        $output->writeln("Validating configuration...");

        try {
            $store = $this->storeManager->getDefaultStoreView();
            $output->writeln("Default store: " . $store->getName());

            // Test service instantiation
            $output->writeln("✓ FreeShippingService instantiated successfully");
            $output->writeln("✓ PerSellerShippingRateCollector instantiated successfully");

            // Test logging
            $this->freeShippingService->logFreeShippingConfiguration();
            $output->writeln("✓ Free shipping configuration logged");

            $this->freeShippingService->logAllFreeShippingTableRates();
            $output->writeln("✓ All free shipping table rates logged");

            $output->writeln("✓ All configuration tests passed");

        } catch (\Exception $e) {
            $output->writeln('<error>Configuration test failed: ' . $e->getMessage() . '</error>');
            throw $e;
        }

        $output->writeln('');
    }
}
