<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model;

use Coditron\CustomShippingRate\Api\PerSellerShippingManagementInterface;
use Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface;
use Psr\Log\LoggerInterface;

class PerSellerShippingManagement implements PerSellerShippingManagementInterface
{
    public function __construct(
        private readonly CartRepositoryInterface $cartRepository,
        private readonly MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Set per-seller shipping methods for quote
     *
     * @param int $cartId
     * @param PerSellerShippingMethodInterface[] $methods
     * @return bool
     */
    public function setPerSellerShippingMethods(int $cartId, array $methods): bool
    {
        try {
            $quote = $this->cartRepository->get($cartId);
            return $this->processPerSellerMethods($quote, $methods);
        } catch (\Exception $e) {
            $this->logger->error('[PerSellerShippingManagement] Error setting per-seller methods', [
                'cart_id' => $cartId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Set per-seller shipping methods for guest quote
     *
     * @param string $cartId
     * @param PerSellerShippingMethodInterface[] $methods
     * @return bool
     */
    public function setGuestPerSellerShippingMethods(string $cartId, array $methods): bool
    {
        try {
            $quoteId = $this->maskedQuoteIdToQuoteId->execute($cartId);
            $quote = $this->cartRepository->get($quoteId);
            return $this->processPerSellerMethods($quote, $methods);
        } catch (\Exception $e) {
            $this->logger->error('[PerSellerShippingManagement] Error setting guest per-seller methods', [
                'cart_id' => $cartId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get per-seller shipping methods for quote
     *
     * @param int $cartId
     * @return PerSellerShippingMethodInterface[]
     */
    public function getPerSellerShippingMethods(int $cartId): array
    {
        try {
            $quote = $this->cartRepository->get($cartId);
            return $this->extractPerSellerMethods($quote);
        } catch (\Exception $e) {
            $this->logger->error('[PerSellerShippingManagement] Error getting per-seller methods', [
                'cart_id' => $cartId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get per-seller shipping methods for guest quote
     *
     * @param string $cartId
     * @return PerSellerShippingMethodInterface[]
     */
    public function getGuestPerSellerShippingMethods(string $cartId): array
    {
        try {
            $quoteId = $this->maskedQuoteIdToQuoteId->execute($cartId);
            $quote = $this->cartRepository->get($quoteId);
            return $this->extractPerSellerMethods($quote);
        } catch (\Exception $e) {
            $this->logger->error('[PerSellerShippingManagement] Error getting guest per-seller methods', [
                'cart_id' => $cartId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Process per-seller methods for quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @param PerSellerShippingMethodInterface[] $methods
     * @return bool
     */
    private function processPerSellerMethods($quote, array $methods): bool
    {
        $perSellerData = [];
        $totalCost = 0;

        foreach ($methods as $method) {
            $sellerId = $method->getSellerId();
            $perSellerData[$sellerId] = [
                'seller_id' => $sellerId,
                'method_code' => $method->getMethodCode(),
                'carrier_code' => $method->getCarrierCode(),
                'method_title' => $method->getMethodTitle(),
                'price' => $method->getPrice()
            ];
            $totalCost += $method->getPrice();
        }

        // Store per-seller data in quote
        $quote->setShippingData(json_encode($perSellerData));
        
        // Set the combined shipping method
        $shippingAddress = $quote->getShippingAddress();
        $shippingAddress->setShippingMethod('customshippingrate_per_seller_combined');
        $shippingAddress->setShippingAmount($totalCost);
        $shippingAddress->setBaseShippingAmount($totalCost);

        $this->cartRepository->save($quote);

        $this->logger->info('[PerSellerShippingManagement] Set per-seller shipping methods', [
            'quote_id' => $quote->getId(),
            'methods_count' => count($methods),
            'total_cost' => $totalCost,
            'per_seller_data' => $perSellerData
        ]);

        return true;
    }

    /**
     * Extract per-seller methods from quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @return PerSellerShippingMethodInterface[]
     */
    private function extractPerSellerMethods($quote): array
    {
        $shippingData = $quote->getShippingData();
        
        if (!$shippingData) {
            return [];
        }

        try {
            $data = json_decode($shippingData, true);
            $methods = [];

            if (is_array($data)) {
                foreach ($data as $sellerId => $methodData) {
                    if (is_array($methodData) && isset($methodData['method_code'])) {
                        $method = new \Coditron\CustomShippingRate\Model\Data\PerSellerShippingMethod();
                        $method->setSellerId((int)$sellerId);
                        $method->setMethodCode($methodData['method_code']);
                        $method->setCarrierCode($methodData['carrier_code'] ?? '');
                        $method->setMethodTitle($methodData['method_title'] ?? '');
                        $method->setPrice((float)($methodData['price'] ?? 0));
                        
                        $methods[] = $method;
                    }
                }
            }

            return $methods;
        } catch (\Exception $e) {
            $this->logger->error('[PerSellerShippingManagement] Error extracting per-seller methods', [
                'quote_id' => $quote->getId(),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
