<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\Data;

use Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface;
use Magento\Framework\DataObject;

class PerSellerShippingMethod extends DataObject implements PerSellerShippingMethodInterface
{
    /**
     * Get seller ID
     *
     * @return int
     */
    public function getSellerId(): int
    {
        return (int)$this->getData(self::SELLER_ID);
    }

    /**
     * Set seller ID
     *
     * @param int $sellerId
     * @return $this
     */
    public function setSellerId(int $sellerId): self
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    /**
     * Get method code
     *
     * @return string
     */
    public function getMethodCode(): string
    {
        return (string)$this->getData(self::METHOD_CODE);
    }

    /**
     * Set method code
     *
     * @param string $methodCode
     * @return $this
     */
    public function setMethodCode(string $methodCode): self
    {
        return $this->setData(self::METHOD_CODE, $methodCode);
    }

    /**
     * Get carrier code
     *
     * @return string
     */
    public function getCarrierCode(): string
    {
        return (string)$this->getData(self::CARRIER_CODE);
    }

    /**
     * Set carrier code
     *
     * @param string $carrierCode
     * @return $this
     */
    public function setCarrierCode(string $carrierCode): self
    {
        return $this->setData(self::CARRIER_CODE, $carrierCode);
    }

    /**
     * Get method title
     *
     * @return string
     */
    public function getMethodTitle(): string
    {
        return (string)$this->getData(self::METHOD_TITLE);
    }

    /**
     * Set method title
     *
     * @param string $methodTitle
     * @return $this
     */
    public function setMethodTitle(string $methodTitle): self
    {
        return $this->setData(self::METHOD_TITLE, $methodTitle);
    }

    /**
     * Get price
     *
     * @return float
     */
    public function getPrice(): float
    {
        return (float)$this->getData(self::PRICE);
    }

    /**
     * Set price
     *
     * @param float $price
     * @return $this
     */
    public function setPrice(float $price): self
    {
        return $this->setData(self::PRICE, $price);
    }
}
