<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\Quote\Total;

use Magento\Quote\Model\Quote;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Quote\Model\Quote\Address\Total\AbstractTotal;
use Psr\Log\LoggerInterface;

class PerSellerShipping extends AbstractTotal
{
    public function __construct(
        private readonly LoggerInterface $logger
    ) {
        $this->setCode('per_seller_shipping');
    }

    /**
     * Collect per-seller shipping totals
     *
     * @param Quote $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @param Total $total
     * @return $this
     */
    public function collect(
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ): self {
        parent::collect($quote, $shippingAssignment, $total);

        $address = $shippingAssignment->getShipping()->getAddress();
        $shippingMethod = $address->getShippingMethod();

        // Only process if this is our per-seller shipping method
        if ($shippingMethod !== 'customshippingrate_per_seller_combined') {
            return $this;
        }

        $this->logger->info('[PerSellerShipping] Processing per-seller shipping total calculation', [
            'quote_id' => $quote->getId(),
            'shipping_method' => $shippingMethod
        ]);

        // Get per-seller shipping data from quote
        $perSellerData = $this->getPerSellerShippingData($quote);
        
        if (empty($perSellerData)) {
            $this->logger->warning('[PerSellerShipping] No per-seller shipping data found');
            return $this;
        }

        // Calculate total shipping cost
        $totalShippingCost = $this->calculateTotalShippingCost($perSellerData);

        $this->logger->info('[PerSellerShipping] Calculated total shipping cost', [
            'total_cost' => $totalShippingCost,
            'per_seller_data' => $perSellerData
        ]);

        // Set the shipping amount
        $total->setShippingAmount($totalShippingCost);
        $total->setBaseShippingAmount($totalShippingCost);
        $total->setShippingDescription($this->getShippingDescription($perSellerData));

        // Update quote totals
        $quote->setShippingAmount($totalShippingCost);
        $quote->setBaseShippingAmount($totalShippingCost);

        return $this;
    }

    /**
     * Get per-seller shipping data from quote
     *
     * @param Quote $quote
     * @return array
     */
    private function getPerSellerShippingData(Quote $quote): array
    {
        $shippingData = $quote->getShippingData();
        
        if (!$shippingData) {
            return [];
        }

        try {
            $data = json_decode($shippingData, true);
            return is_array($data) ? $data : [];
        } catch (\Exception $e) {
            $this->logger->error('[PerSellerShipping] Error decoding shipping data', [
                'error' => $e->getMessage(),
                'shipping_data' => $shippingData
            ]);
            return [];
        }
    }

    /**
     * Calculate total shipping cost from per-seller data
     *
     * @param array $perSellerData
     * @return float
     */
    private function calculateTotalShippingCost(array $perSellerData): float
    {
        $totalCost = 0;

        foreach ($perSellerData as $sellerId => $sellerShippingData) {
            if (is_array($sellerShippingData)) {
                foreach ($sellerShippingData as $serviceType => $methodData) {
                    if (isset($methodData['price']) && is_numeric($methodData['price'])) {
                        $totalCost += (float)$methodData['price'];
                        
                        $this->logger->debug('[PerSellerShipping] Added seller shipping cost', [
                            'seller_id' => $sellerId,
                            'service_type' => $serviceType,
                            'price' => $methodData['price'],
                            'running_total' => $totalCost
                        ]);
                    }
                }
            }
        }

        return $totalCost;
    }

    /**
     * Generate shipping description for per-seller shipping
     *
     * @param array $perSellerData
     * @return string
     */
    private function getShippingDescription(array $perSellerData): string
    {
        $sellerCount = count($perSellerData);
        $descriptions = [];

        foreach ($perSellerData as $sellerId => $sellerShippingData) {
            if (is_array($sellerShippingData)) {
                foreach ($sellerShippingData as $serviceType => $methodData) {
                    if (isset($methodData['seller_name'], $methodData['method_title'])) {
                        $descriptions[] = sprintf(
                            '%s: %s',
                            $methodData['seller_name'],
                            $methodData['method_title']
                        );
                    }
                }
            }
        }

        if (empty($descriptions)) {
            return sprintf('Per-Seller Shipping (%d sellers)', $sellerCount);
        }

        return implode('; ', $descriptions);
    }

    /**
     * Fetch per-seller shipping totals
     *
     * @param Quote $quote
     * @param Total $total
     * @return array
     */
    public function fetch(Quote $quote, Total $total): array
    {
        $shippingMethod = $quote->getShippingAddress()->getShippingMethod();
        
        if ($shippingMethod !== 'customshippingrate_per_seller_combined') {
            return [];
        }

        $shippingAmount = $total->getShippingAmount();
        
        if ($shippingAmount <= 0) {
            return [];
        }

        return [
            'code' => $this->getCode(),
            'title' => __('Per-Seller Shipping'),
            'value' => $shippingAmount
        ];
    }

    /**
     * Get label for the total
     *
     * @return string
     */
    public function getLabel(): string
    {
        return __('Per-Seller Shipping');
    }
}
