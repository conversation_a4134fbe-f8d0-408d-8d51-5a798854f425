<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Service;

use Coditron\CustomShippingRate\Model\Command\ProductDataBuilder;
use Coditron\CustomShippingRate\Model\RateProviders\RatePoolProvider;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Shipping\Model\Rate\ResultFactory;
use Magento\Quote\Model\Quote\Address\RateResult\MethodFactory;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\SellerFactory;

class PerSellerShippingRateCollector
{
    public function __construct(
        private readonly ProductDataBuilder $productDataBuilder,
        private readonly RatePoolProvider $ratePoolProvider,
        private readonly ResultFactory $rateFactory,
        private readonly MethodFactory $rateMethodFactory,
        private readonly FreeShippingService $freeShippingService,
        private readonly SellerFactory $sellerFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Collect shipping rates grouped by seller
     *
     * @param RateRequest $request
     * @param array $serviceTypeCodes
     * @param string $carrierCode
     * @param string $carrierTitle
     * @return \Magento\Shipping\Model\Rate\Result
     */
    public function collectPerSellerRates(
        RateRequest $request,
        array $serviceTypeCodes,
        string $carrierCode,
        string $carrierTitle
    ): \Magento\Shipping\Model\Rate\Result {
        $this->logger->info('[PerSellerShippingRateCollector] Starting per-seller rate collection');

        $result = $this->rateFactory->create();
        
        if (!isset($request->getAllItems()[0])) {
            $this->logger->warning('[PerSellerShippingRateCollector] No quote items found');
            return $result;
        }

        $quote = current($request->getAllItems())->getQuote();
        $items = $quote->getAllVisibleItems();
        $country = $request->getDestCountryId();

        // Group items by seller
        $toShipPerSellerArray = $this->productDataBuilder->build($items, $country);
        
        if (empty($toShipPerSellerArray)) {
            $this->logger->warning('[PerSellerShippingRateCollector] No seller data found');
            return $result;
        }

        // Analyze quote items for free shipping evaluation
        $sellerData = $this->freeShippingService->analyzeQuoteItemsBySeller($items);
        $perSellerFreeShipping = $this->freeShippingService->evaluatePerSellerFreeShipping($sellerData, $country);

        $this->logger->info('[PerSellerShippingRateCollector] Collected seller data', [
            'seller_count' => count($toShipPerSellerArray),
            'sellers' => array_keys($toShipPerSellerArray)
        ]);

        // Collect rates for each seller separately
        foreach ($toShipPerSellerArray as $sellerKey => $sellerShipData) {
            [$sellerIdentifier, $sellerId] = explode('|', $sellerKey);
            
            // Get seller information for display
            $sellerInfo = $this->getSellerInfo($sellerId);
            $sellerName = $sellerInfo['name'] ?? "Seller {$sellerId}";

            $rateProvider = $this->ratePoolProvider->getBySeller(
                $sellerKey,
                $sellerShipData + ['services' => $serviceTypeCodes]
            );

            $tableRatesByServiceType = $rateProvider->get($request, $sellerIdentifier, $sellerId);

            $this->logger->info('[PerSellerShippingRateCollector] Processing rates for seller', [
                'seller_key' => $sellerKey,
                'seller_id' => $sellerId,
                'seller_name' => $sellerName,
                'available_services' => array_keys($tableRatesByServiceType)
            ]);

            // Check if this seller qualifies for free shipping
            $qualifiesForFreeShipping = isset($perSellerFreeShipping[$sellerId]) && 
                                       $perSellerFreeShipping[$sellerId]['qualifies_for_free_shipping'];

            // Create shipping methods for each service type for this seller
            foreach ($serviceTypeCodes as $serviceTypeCode => $serviceTypeTitle) {
                $tableRate = $tableRatesByServiceType[$serviceTypeCode] ??
                           reset($tableRatesByServiceType);

                if (!$tableRate instanceof ShipTableRates) {
                    continue;
                }

                $price = $tableRate->getShippingPrice();
                $leadTime = $tableRate->getTotalLeadTime();

                // Create both free shipping (if qualified) and standard shipping options
                if ($qualifiesForFreeShipping && $tableRate->getFreeShipping()) {
                    // Add free shipping option
                    $freeRate = $this->rateMethodFactory->create();
                    $freeRate->setCarrier($carrierCode);
                    $freeRate->setCarrierTitle($carrierTitle);
                    $freeRate->setMethod('free_' . $serviceTypeCode . '_seller_' . $sellerId);
                    $freeRate->setMethodTitle(sprintf('%s: Free Shipping', $sellerName));
                    $freeRate->setCost(0);
                    $freeRate->setPrice(0);
                    $freeRate->setData('seller_id', $sellerId);
                    $freeRate->setData('seller_name', $sellerName);
                    $freeRate->setData('service_type', $serviceTypeCode);
                    $freeRate->setData('lead_time', $leadTime);
                    $freeRate->setData('qualifies_free_shipping', true);
                    $freeRate->setData('is_free_option', true);

                    $result->append($freeRate);

                    $this->logger->info('[PerSellerShippingRateCollector] Added FREE rate for seller', [
                        'seller_id' => $sellerId,
                        'seller_name' => $sellerName,
                        'service_type' => $serviceTypeCode,
                        'method_title' => sprintf('%s: Free Shipping', $sellerName),
                        'price' => 0
                    ]);
                }

                // Always add standard shipping option
                $methodTitle = sprintf('%s: %s', $sellerName, $serviceTypeTitle);
                if ($leadTime > 0) {
                    $methodTitle .= sprintf(' (%d days)', $leadTime);
                }

                $standardRate = $this->rateMethodFactory->create();
                $standardRate->setCarrier($carrierCode);
                $standardRate->setCarrierTitle($carrierTitle);
                $standardRate->setMethod('standard_' . $serviceTypeCode . '_seller_' . $sellerId);
                $standardRate->setMethodTitle($methodTitle);
                $standardRate->setCost($price);
                $standardRate->setPrice($price);
                $standardRate->setData('seller_id', $sellerId);
                $standardRate->setData('seller_name', $sellerName);
                $standardRate->setData('service_type', $serviceTypeCode);
                $standardRate->setData('lead_time', $leadTime);
                $standardRate->setData('qualifies_free_shipping', $qualifiesForFreeShipping);
                $standardRate->setData('is_free_option', false);

                $result->append($standardRate);

                $this->logger->info('[PerSellerShippingRateCollector] Added STANDARD rate for seller', [
                    'seller_id' => $sellerId,
                    'seller_name' => $sellerName,
                    'service_type' => $serviceTypeCode,
                    'method_title' => $methodTitle,
                    'price' => $price,
                    'qualifies_free_shipping' => $qualifiesForFreeShipping
                ]);
            }
        }

        $this->logger->info('[PerSellerShippingRateCollector] Completed per-seller rate collection', [
            'total_rates' => count($result->getAllRates())
        ]);

        return $result;
    }

    /**
     * Get seller information for display
     *
     * @param int $sellerId
     * @return array
     */
    private function getSellerInfo(int $sellerId): array
    {
        try {
            $seller = $this->sellerFactory->create()->load($sellerId, 'seller_id');
            
            if ($seller->getId()) {
                return [
                    'id' => $seller->getSellerId(),
                    'name' => $seller->getShopTitle() ?: $seller->getShopUrl() ?: "Seller {$sellerId}",
                    'shop_url' => $seller->getShopUrl()
                ];
            }
        } catch (\Exception $e) {
            $this->logger->warning('[PerSellerShippingRateCollector] Could not load seller info', [
                'seller_id' => $sellerId,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'id' => $sellerId,
            'name' => "Seller {$sellerId}",
            'shop_url' => null
        ];
    }

    /**
     * Calculate total shipping cost from selected per-seller rates
     *
     * @param array $selectedRates Array of selected rate method codes
     * @param \Magento\Shipping\Model\Rate\Result $allRates
     * @return float
     */
    public function calculateTotalShippingCost(array $selectedRates, \Magento\Shipping\Model\Rate\Result $allRates): float
    {
        $totalCost = 0;
        $ratesByMethod = [];

        // Index rates by method code
        foreach ($allRates->getAllRates() as $rate) {
            $ratesByMethod[$rate->getMethod()] = $rate;
        }

        // Sum up costs for selected rates
        foreach ($selectedRates as $methodCode) {
            if (isset($ratesByMethod[$methodCode])) {
                $totalCost += $ratesByMethod[$methodCode]->getPrice();
            }
        }

        $this->logger->info('[PerSellerShippingRateCollector] Calculated total shipping cost', [
            'selected_rates' => $selectedRates,
            'total_cost' => $totalCost
        ]);

        return $totalCost;
    }
}
