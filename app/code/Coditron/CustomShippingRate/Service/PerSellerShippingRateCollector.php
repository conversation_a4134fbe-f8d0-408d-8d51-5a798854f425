<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Service;

use Coditron\CustomShippingRate\Model\Command\ProductDataBuilder;
use Coditron\CustomShippingRate\Model\RateProviders\RatePoolProvider;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Shipping\Model\Rate\ResultFactory;
use Magento\Quote\Model\Quote\Address\RateResult\MethodFactory;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\SellerFactory;

class PerSellerShippingRateCollector
{
    public function __construct(
        private readonly ProductDataBuilder $productDataBuilder,
        private readonly RatePoolProvider $ratePoolProvider,
        private readonly ResultFactory $rateFactory,
        private readonly MethodFactory $rateMethodFactory,
        private readonly FreeShippingService $freeShippingService,
        private readonly SellerFactory $sellerFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Collect shipping rates grouped by seller
     *
     * @param RateRequest $request
     * @param array $serviceTypeCodes
     * @param string $carrierCode
     * @param string $carrierTitle
     * @return \Magento\Shipping\Model\Rate\Result
     */
    public function collectPerSellerRates(
        RateRequest $request,
        array $serviceTypeCodes,
        string $carrierCode,
        string $carrierTitle
    ): \Magento\Shipping\Model\Rate\Result {
        $this->logger->info('[PerSellerShippingRateCollector] Starting per-seller rate collection');

        $result = $this->rateFactory->create();
        
        if (!isset($request->getAllItems()[0])) {
            $this->logger->warning('[PerSellerShippingRateCollector] No quote items found');
            return $result;
        }

        $quote = current($request->getAllItems())->getQuote();
        $items = $quote->getAllVisibleItems();
        $country = $request->getDestCountryId();

        // Group items by seller
        $toShipPerSellerArray = $this->productDataBuilder->build($items, $country);
        
        if (empty($toShipPerSellerArray)) {
            $this->logger->warning('[PerSellerShippingRateCollector] No seller data found');
            return $result;
        }

        // Analyze quote items for free shipping evaluation
        $sellerData = $this->freeShippingService->analyzeQuoteItemsBySeller($items);
        $perSellerFreeShipping = $this->freeShippingService->evaluatePerSellerFreeShipping($sellerData, $country);

        $this->logger->info('[PerSellerShippingRateCollector] Collected seller data', [
            'seller_count' => count($toShipPerSellerArray),
            'sellers' => array_keys($toShipPerSellerArray)
        ]);

        // Collect rates for each seller separately
        foreach ($toShipPerSellerArray as $sellerKey => $sellerShipData) {
            [$sellerIdentifier, $sellerId] = explode('|', $sellerKey);
            
            // Get seller information for display
            $sellerInfo = $this->getSellerInfo($sellerId);
            $sellerName = $sellerInfo['name'] ?? "Seller {$sellerId}";

            $rateProvider = $this->ratePoolProvider->getBySeller(
                $sellerKey,
                $sellerShipData + ['services' => $serviceTypeCodes]
            );

            $tableRatesByServiceType = $rateProvider->get($request, $sellerIdentifier, $sellerId);

            $this->logger->info('[PerSellerShippingRateCollector] Processing rates for seller', [
                'seller_key' => $sellerKey,
                'seller_id' => $sellerId,
                'seller_name' => $sellerName,
                'available_services' => array_keys($tableRatesByServiceType)
            ]);

            // Check if this seller qualifies for free shipping
            $qualifiesForFreeShipping = isset($perSellerFreeShipping[$sellerId]) && 
                                       $perSellerFreeShipping[$sellerId]['qualifies_for_free_shipping'];

            // Create shipping methods for each service type for this seller
            foreach ($serviceTypeCodes as $serviceTypeCode => $serviceTypeTitle) {
                $tableRate = $tableRatesByServiceType[$serviceTypeCode] ?? 
                           reset($tableRatesByServiceType);

                if (!$tableRate instanceof ShipTableRates) {
                    continue;
                }

                $price = $tableRate->getShippingPrice();
                $leadTime = $tableRate->getTotalLeadTime();

                // Apply free shipping if seller qualifies
                if ($qualifiesForFreeShipping && $tableRate->getFreeShipping()) {
                    $price = 0;
                    $methodTitle = sprintf('%s: Free Shipping', $sellerName);
                } else {
                    $methodTitle = sprintf('%s: %s', $sellerName, $serviceTypeTitle);
                    if ($leadTime > 0) {
                        $methodTitle .= sprintf(' (%d days)', $leadTime);
                    }
                }

                // Create rate method
                $rate = $this->rateMethodFactory->create();
                $rate->setCarrier($carrierCode);
                $rate->setCarrierTitle($carrierTitle);
                $rate->setMethod($serviceTypeCode . '_seller_' . $sellerId);
                $rate->setMethodTitle($methodTitle);
                $rate->setCost($price);
                $rate->setPrice($price);

                // Add seller-specific data for later processing
                $rate->setData('seller_id', $sellerId);
                $rate->setData('seller_name', $sellerName);
                $rate->setData('service_type', $serviceTypeCode);
                $rate->setData('lead_time', $leadTime);
                $rate->setData('qualifies_free_shipping', $qualifiesForFreeShipping);

                $result->append($rate);

                $this->logger->info('[PerSellerShippingRateCollector] Added rate for seller', [
                    'seller_id' => $sellerId,
                    'seller_name' => $sellerName,
                    'service_type' => $serviceTypeCode,
                    'method_title' => $methodTitle,
                    'price' => $price,
                    'qualifies_free_shipping' => $qualifiesForFreeShipping
                ]);
            }
        }

        $this->logger->info('[PerSellerShippingRateCollector] Completed per-seller rate collection', [
            'total_rates' => count($result->getAllRates())
        ]);

        return $result;
    }

    /**
     * Get seller information for display
     *
     * @param int $sellerId
     * @return array
     */
    private function getSellerInfo(int $sellerId): array
    {
        try {
            $seller = $this->sellerFactory->create()->load($sellerId, 'seller_id');
            
            if ($seller->getId()) {
                return [
                    'id' => $seller->getSellerId(),
                    'name' => $seller->getShopTitle() ?: $seller->getShopUrl() ?: "Seller {$sellerId}",
                    'shop_url' => $seller->getShopUrl()
                ];
            }
        } catch (\Exception $e) {
            $this->logger->warning('[PerSellerShippingRateCollector] Could not load seller info', [
                'seller_id' => $sellerId,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'id' => $sellerId,
            'name' => "Seller {$sellerId}",
            'shop_url' => null
        ];
    }

    /**
     * Calculate total shipping cost from selected per-seller rates
     *
     * @param array $selectedRates Array of selected rate method codes
     * @param \Magento\Shipping\Model\Rate\Result $allRates
     * @return float
     */
    public function calculateTotalShippingCost(array $selectedRates, \Magento\Shipping\Model\Rate\Result $allRates): float
    {
        $totalCost = 0;
        $ratesByMethod = [];

        // Index rates by method code
        foreach ($allRates->getAllRates() as $rate) {
            $ratesByMethod[$rate->getMethod()] = $rate;
        }

        // Sum up costs for selected rates
        foreach ($selectedRates as $methodCode) {
            if (isset($ratesByMethod[$methodCode])) {
                $totalCost += $ratesByMethod[$methodCode]->getPrice();
            }
        }

        $this->logger->info('[PerSellerShippingRateCollector] Calculated total shipping cost', [
            'selected_rates' => $selectedRates,
            'total_cost' => $totalCost
        ]);

        return $totalCost;
    }
}
