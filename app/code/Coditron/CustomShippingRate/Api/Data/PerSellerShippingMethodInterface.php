<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Api\Data;

interface PerSellerShippingMethodInterface
{
    public const SELLER_ID = 'seller_id';
    public const METHOD_CODE = 'method_code';
    public const CARRIER_CODE = 'carrier_code';
    public const METHOD_TITLE = 'method_title';
    public const PRICE = 'price';

    /**
     * Get seller ID
     *
     * @return int
     */
    public function getSellerId(): int;

    /**
     * Set seller ID
     *
     * @param int $sellerId
     * @return $this
     */
    public function setSellerId(int $sellerId): self;

    /**
     * Get method code
     *
     * @return string
     */
    public function getMethodCode(): string;

    /**
     * Set method code
     *
     * @param string $methodCode
     * @return $this
     */
    public function setMethodCode(string $methodCode): self;

    /**
     * Get carrier code
     *
     * @return string
     */
    public function getCarrierCode(): string;

    /**
     * Set carrier code
     *
     * @param string $carrierCode
     * @return $this
     */
    public function setCarrierCode(string $carrierCode): self;

    /**
     * Get method title
     *
     * @return string
     */
    public function getMethodTitle(): string;

    /**
     * Set method title
     *
     * @param string $methodTitle
     * @return $this
     */
    public function setMethodTitle(string $methodTitle): self;

    /**
     * Get price
     *
     * @return float
     */
    public function getPrice(): float;

    /**
     * Set price
     *
     * @param float $price
     * @return $this
     */
    public function setPrice(float $price): self;
}
