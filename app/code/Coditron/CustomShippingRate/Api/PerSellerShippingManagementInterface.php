<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Api;

interface PerSellerShippingManagementInterface
{
    /**
     * Set per-seller shipping methods for quote
     *
     * @param int $cartId
     * @param \Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface[] $methods
     * @return bool
     */
    public function setPerSellerShippingMethods(int $cartId, array $methods): bool;

    /**
     * Set per-seller shipping methods for guest quote
     *
     * @param string $cartId
     * @param \Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface[] $methods
     * @return bool
     */
    public function setGuestPerSellerShippingMethods(string $cartId, array $methods): bool;

    /**
     * Get per-seller shipping methods for quote
     *
     * @param int $cartId
     * @return \Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface[]
     */
    public function getPerSellerShippingMethods(int $cartId): array;

    /**
     * Get per-seller shipping methods for guest quote
     *
     * @param string $cartId
     * @return \Coditron\CustomShippingRate\Api\Data\PerSellerShippingMethodInterface[]
     */
    public function getGuestPerSellerShippingMethods(string $cartId): array;
}
