<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\OfflineShipping\Model\Carrier\Freeshipping;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Service\FreeShippingService;
use Psr\Log\LoggerInterface;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Enhance core freeshipping with custom thresholds when they exist and are met
     *
     * @param Freeshipping $subject
     * @param callable $proceed
     * @param RateRequest $request
     * @return \Magento\Shipping\Model\Rate\Result|bool
     */
    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        $this->logger->info('[FreeShippingPlugin] Starting per-seller free shipping rate collection', [
            'country' => $country,
            'subtotal' => $subtotal,
            'dest_city' => $request->getDestCity(),
            'dest_postcode' => $request->getDestPostcode(),
            'package_weight' => $request->getPackageWeight(),
            'package_value' => $request->getPackageValue(),
            'free_shipping' => $request->getFreeShipping()
        ]);

        // Check if the carrier is enabled and log configuration
        $isEnabled = $subject->getConfigFlag('active');
        $this->logger->info('[FreeShippingPlugin] Free shipping carrier status', [
            'is_enabled' => $isEnabled,
            'carrier_code' => $subject->getCarrierCode(),
            'active_config' => $subject->getConfigData('active'),
            'title_config' => $subject->getConfigData('title'),
            'name_config' => $subject->getConfigData('name'),
            'subtotal_config' => $subject->getConfigData('free_shipping_subtotal')
        ]);

        // Log the complete Magento free shipping configuration
        $this->freeShippingService->logFreeShippingConfiguration();

        // Log all free shipping table rates for debugging
        $this->freeShippingService->logAllFreeShippingTableRates();

        // Get quote items for per-seller analysis
        $quoteItems = [];
        if (!empty($request->getAllItems())) {
            $quote = current($request->getAllItems())->getQuote();
            $quoteItems = $quote->getAllVisibleItems();
        }

        if (empty($quoteItems)) {
            $this->logger->warning('[FreeShippingPlugin] No quote items found for per-seller analysis');
            return false;
        }

        // Analyze quote items by seller
        $sellerData = $this->freeShippingService->analyzeQuoteItemsBySeller($quoteItems);

        if (empty($sellerData)) {
            $this->logger->warning('[FreeShippingPlugin] No seller data found in quote items');
            return false;
        }

        // Evaluate per-seller free shipping
        $perSellerResults = $this->freeShippingService->evaluatePerSellerFreeShipping($sellerData, $country);

        $this->logger->info('[FreeShippingPlugin] Per-seller free shipping evaluation results', [
            'seller_count' => count($perSellerResults),
            'results' => array_map(function($result) {
                return [
                    'seller_id' => $result['seller_id'],
                    'subtotal' => $result['subtotal'],
                    'has_thresholds' => $result['has_thresholds'],
                    'qualifies' => $result['qualifies_for_free_shipping'],
                    'met_thresholds_count' => count($result['met_thresholds'])
                ];
            }, $perSellerResults)
        ]);

        // Determine if we should allow free shipping based on per-seller evaluation
        $shouldAllowFreeShipping = false;
        $blockingReason = '';
        $qualifyingSellers = [];
        $sellersWithThresholds = [];

        foreach ($perSellerResults as $sellerId => $result) {
            if ($result['has_thresholds']) {
                $sellersWithThresholds[] = $sellerId;
                if ($result['qualifies_for_free_shipping']) {
                    $qualifyingSellers[] = $sellerId;
                }
            }
        }

        if (empty($sellersWithThresholds)) {
            // No sellers have thresholds defined for this country
            $blockingReason = 'No sellers have free shipping thresholds defined for country';
            $this->logger->info('[FreeShippingPlugin] ' . $blockingReason . ' - blocking free shipping');
        } elseif (count($qualifyingSellers) === count($sellersWithThresholds)) {
            // All sellers with thresholds qualify for free shipping
            $shouldAllowFreeShipping = true;
            $this->logger->info('[FreeShippingPlugin] All sellers qualify for free shipping - allowing free shipping', [
                'qualifying_sellers' => $qualifyingSellers
            ]);
        } else {
            // Some sellers don't qualify for free shipping
            $blockingReason = 'Not all sellers qualify for free shipping';
            $this->logger->info('[FreeShippingPlugin] ' . $blockingReason . ' - blocking free shipping', [
                'sellers_with_thresholds' => $sellersWithThresholds,
                'qualifying_sellers' => $qualifyingSellers
            ]);
        }

        if (!$shouldAllowFreeShipping) {
            $this->logger->info('[FreeShippingPlugin] Free shipping blocked', ['reason' => $blockingReason]);
            return false;
        }

        // Proceed with the original logic
        $this->logger->info('[FreeShippingPlugin] Proceeding with original free shipping logic');
        try {
            $result = $proceed($request);
            $this->logger->info('[FreeShippingPlugin] Original logic completed', [
                'result_type' => $result ? get_class($result) : 'false/null',
                'has_rates' => $result && $result->getAllRates() ? count($result->getAllRates()) : 0
            ]);
        } catch (\Exception $e) {
            $this->logger->error('[FreeShippingPlugin] Error during original logic execution', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            return false;
        }

        // If we have qualifying sellers, modify the result to show per-seller information
        if (!empty($qualifyingSellers) && $result && $result->getAllRates()) {
            $this->logger->info('[FreeShippingPlugin] Adding per-seller subtitles to existing rates');
            try {
                $this->freeShippingService->addPerSellerSubtitle($result, $perSellerResults, $qualifyingSellers);
                $this->logger->info('[FreeShippingPlugin] Per-seller subtitles added successfully');
            } catch (\Exception $e) {
                $this->logger->error('[FreeShippingPlugin] Error adding per-seller subtitles', [
                    'error_message' => $e->getMessage()
                ]);
                // Don't return false here, just log the error and continue
            }
        }

        // Log the final result
        if ($result === false) {
            $this->logger->warning('[FreeShippingPlugin] Free shipping returned false - no rates available');
        } elseif ($result && $result->getAllRates()) {
            $rates = $result->getAllRates();
            $this->logger->info('[FreeShippingPlugin] Free shipping rates collected', [
                'rate_count' => count($rates),
                'rates' => array_map(function($rate) {
                    return [
                        'carrier' => $rate->getCarrier(),
                        'method' => $rate->getMethod(),
                        'method_title' => $rate->getMethodTitle(),
                        'price' => $rate->getPrice(),
                        'cost' => $rate->getCost()
                    ];
                }, $rates)
            ]);
        } else {
            $this->logger->info('[FreeShippingPlugin] Free shipping result exists but no rates found', [
                'result_type' => $result ? get_class($result) : 'null',
                'result_data' => $result ? $result->getData() : null
            ]);
        }

        $this->logger->info('[FreeShippingPlugin] Completed free shipping rate collection');
        return $result;
    }
}
