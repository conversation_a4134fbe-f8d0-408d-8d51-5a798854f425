<!-- ko if: isPerSellerMode() -->
<div class="per-seller-shipping-container">
    <div class="per-seller-shipping-header">
        <h3 data-bind="i18n: 'Select Shipping Method for Each Seller'"></h3>
        <p class="note" data-bind="i18n: 'You need to select a shipping method for each seller in your cart.'"></p>
    </div>

    <!-- Free Shipping Section -->
    <!-- ko if: freeShippingMethods().length > 0 -->
    <div class="shipping-type-group free-shipping-group">
        <div class="shipping-type-header">
            <h4 class="shipping-type-title free-title" data-bind="i18n: 'Free Shipping'"></h4>
        </div>

        <div class="shipping-methods-list">
            <!-- ko foreach: freeShippingMethods -->
            <div class="shipping-method-item free-shipping-item">
                <input type="radio"
                       class="radio"
                       data-bind="
                           attr: {
                               id: 'free_shipping_method_' + sellerId + '_' + $index(),
                               name: 'shipping_method_seller_' + sellerId,
                               value: code
                           },
                           checked: $parent.getSelectedMethodForSeller(sellerId),
                           click: function() { $parent.selectMethodForSeller(sellerId, code); }
                       " />

                <label class="shipping-method-label free-shipping-label"
                       data-bind="attr: { for: 'free_shipping_method_' + sellerId + '_' + $index() }">
                    <span class="method-title" data-bind="text: 'Free (' + sellerName + ')'"></span>
                    <span class="method-price free-price">€0.00</span>
                </label>
            </div>
            <!-- /ko -->
        </div>
    </div>
    <!-- /ko -->

    <!-- Standard Shipping Section -->
    <!-- ko if: standardShippingMethods().length > 0 -->
    <div class="shipping-type-group standard-shipping-group">
        <div class="shipping-type-header">
            <h4 class="shipping-type-title standard-title" data-bind="i18n: 'Standard Shipping'"></h4>
        </div>

        <div class="shipping-methods-list">
            <!-- ko foreach: standardShippingMethods -->
            <div class="shipping-method-item standard-shipping-item">
                <input type="radio"
                       class="radio"
                       data-bind="
                           attr: {
                               id: 'standard_shipping_method_' + sellerId + '_' + $index(),
                               name: 'shipping_method_seller_' + sellerId,
                               value: code
                           },
                           checked: $parent.getSelectedMethodForSeller(sellerId),
                           click: function() { $parent.selectMethodForSeller(sellerId, code); }
                       " />

                <label class="shipping-method-label standard-shipping-label"
                       data-bind="attr: { for: 'standard_shipping_method_' + sellerId + '_' + $index() }">
                    <span class="method-title" data-bind="text: 'Fixed - (' + sellerName + ')'"></span>
                    <span class="method-price" data-bind="text: $parent.formatPrice(price)"></span>
                </label>
            </div>
            <!-- /ko -->
        </div>
    </div>
    <!-- /ko -->

    <div class="per-seller-shipping-summary">
        <div class="shipping-total">
            <span class="label" data-bind="i18n: 'Total Shipping Cost:'"></span>
            <span class="price" data-bind="text: formatPrice(totalShippingPrice())"></span>
        </div>
        
        <!-- ko if: !isComplete() -->
        <div class="incomplete-notice">
            <span class="message" data-bind="i18n: 'Please select shipping methods for all sellers to continue.'"></span>
        </div>
        <!-- /ko -->
    </div>
</div>
<!-- /ko -->

<style>
.per-seller-shipping-container {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.per-seller-shipping-header h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.per-seller-shipping-header .note {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    font-style: italic;
}

.shipping-type-group {
    margin: 20px 0;
    padding: 15px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.free-shipping-group {
    border: 2px solid #28a745;
    background-color: #f8fff9;
}

.standard-shipping-group {
    border: 2px solid #6c757d;
    background-color: #f8f9fa;
}

.shipping-type-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.shipping-type-title {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.free-title {
    color: #28a745;
}

.standard-title {
    color: #6c757d;
}

.shipping-methods-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.shipping-method-item {
    margin: 8px 0;
    display: flex;
    align-items: center;
}

.shipping-method-item .radio {
    margin-right: 10px;
}

.shipping-method-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    cursor: pointer;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f8f8f8;
    transition: background-color 0.2s;
}

.shipping-method-label:hover {
    background-color: #e8f4f8;
}

.shipping-method-item input[type="radio"]:checked + .shipping-method-label {
    background-color: #e8f4f8;
    border-color: #2c5aa0;
}

.free-shipping-item input[type="radio"]:checked + .free-shipping-label {
    background-color: #d4edda;
    border-color: #28a745;
}

.free-shipping-label {
    border-color: #28a745;
}

.free-shipping-label:hover {
    background-color: #d4edda;
}

.free-price {
    color: #28a745 !important;
    font-weight: bold;
    font-size: 14px;
}

.method-title {
    font-weight: 500;
    color: #333;
}

.method-price {
    font-weight: bold;
    color: #2c5aa0;
}

.per-seller-shipping-summary {
    margin-top: 20px;
    padding: 15px;
    border-top: 2px solid #ddd;
    background-color: #f0f8ff;
}

.shipping-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
}

.shipping-total .label {
    color: #333;
}

.shipping-total .price {
    color: #2c5aa0;
    font-size: 18px;
}

.incomplete-notice {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 3px;
    color: #856404;
    text-align: center;
}

.incomplete-notice .message {
    font-weight: 500;
}
</style>
