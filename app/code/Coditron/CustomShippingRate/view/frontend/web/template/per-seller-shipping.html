<!-- ko if: isPerSellerMode() -->
<div class="per-seller-shipping-container">
    <div class="per-seller-shipping-header">
        <h3 data-bind="i18n: 'Select Shipping Method for Each Seller'"></h3>
        <p class="note" data-bind="i18n: 'You need to select a shipping method for each seller in your cart.'"></p>
    </div>

    <!-- ko foreach: sellerGroups -->
    <div class="seller-shipping-group" data-bind="css: 'seller-' + sellerId">
        <div class="seller-header">
            <h4 class="seller-name" data-bind="text: sellerName"></h4>
            <!-- ko if: hasFreeShipping -->
            <span class="free-shipping-badge" data-bind="i18n: 'Free Shipping Available'"></span>
            <!-- /ko -->
        </div>

        <!-- Separate free shipping options -->
        <!-- ko if: hasFreeShipping -->
        <div class="free-shipping-section">
            <h5 class="section-title free-title" data-bind="i18n: 'Free Shipping Options'"></h5>
            <!-- ko foreach: freeShippingMethods -->
            <div class="shipping-method-item free-shipping-item">
                <input type="radio"
                       class="radio"
                       data-bind="
                           attr: {
                               id: 'shipping_method_' + $parent.sellerId + '_free_' + $index(),
                               name: 'shipping_method_seller_' + $parent.sellerId,
                               value: code
                           },
                           checked: $parents[1].getSelectedMethodForSeller($parent.sellerId),
                           click: function() { $parents[1].selectMethodForSeller($parent.sellerId, code); }
                       " />

                <label class="shipping-method-label free-shipping-label"
                       data-bind="attr: { for: 'shipping_method_' + $parent.sellerId + '_free_' + $index() }">
                    <span class="method-title" data-bind="text: title"></span>
                    <span class="method-price free-price" data-bind="i18n: 'FREE'"></span>
                </label>
            </div>
            <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- Standard shipping options -->
        <div class="standard-shipping-section">
            <!-- ko if: hasFreeShipping -->
            <h5 class="section-title standard-title" data-bind="i18n: 'Standard Shipping Options'"></h5>
            <!-- /ko -->
            <!-- ko foreach: standardShippingMethods -->
            <div class="shipping-method-item standard-shipping-item">
                <input type="radio"
                       class="radio"
                       data-bind="
                           attr: {
                               id: 'shipping_method_' + $parent.sellerId + '_standard_' + $index(),
                               name: 'shipping_method_seller_' + $parent.sellerId,
                               value: code
                           },
                           checked: $parents[1].getSelectedMethodForSeller($parent.sellerId),
                           click: function() { $parents[1].selectMethodForSeller($parent.sellerId, code); }
                       " />

                <label class="shipping-method-label standard-shipping-label"
                       data-bind="attr: { for: 'shipping_method_' + $parent.sellerId + '_standard_' + $index() }">
                    <span class="method-title" data-bind="text: title"></span>
                    <span class="method-price" data-bind="text: $parents[1].formatPrice(price)"></span>
                </label>
            </div>
            <!-- /ko -->
        </div>
    </div>
    <!-- /ko -->

    <div class="per-seller-shipping-summary">
        <div class="shipping-total">
            <span class="label" data-bind="i18n: 'Total Shipping Cost:'"></span>
            <span class="price" data-bind="text: formatPrice(totalShippingPrice())"></span>
        </div>
        
        <!-- ko if: !isComplete() -->
        <div class="incomplete-notice">
            <span class="message" data-bind="i18n: 'Please select shipping methods for all sellers to continue.'"></span>
        </div>
        <!-- /ko -->
    </div>
</div>
<!-- /ko -->

<style>
.per-seller-shipping-container {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.per-seller-shipping-header h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.per-seller-shipping-header .note {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    font-style: italic;
}

.seller-shipping-group {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: #fff;
}

.seller-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.seller-header h4 {
    margin: 0;
    color: #2c5aa0;
    font-size: 16px;
    font-weight: bold;
}

.free-shipping-badge {
    background-color: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.section-title {
    margin: 10px 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.free-title {
    color: #28a745;
}

.standard-title {
    color: #6c757d;
}

.free-shipping-section {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8fff9;
    border: 1px solid #d4edda;
    border-radius: 5px;
}

.standard-shipping-section {
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.seller-shipping-methods {
    margin-left: 10px;
}

.shipping-method-item {
    margin: 8px 0;
    display: flex;
    align-items: center;
}

.shipping-method-item .radio {
    margin-right: 10px;
}

.shipping-method-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    cursor: pointer;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f8f8f8;
    transition: background-color 0.2s;
}

.shipping-method-label:hover {
    background-color: #e8f4f8;
}

.shipping-method-item input[type="radio"]:checked + .shipping-method-label {
    background-color: #e8f4f8;
    border-color: #2c5aa0;
}

.free-shipping-item input[type="radio"]:checked + .free-shipping-label {
    background-color: #d4edda;
    border-color: #28a745;
}

.free-shipping-label {
    border-color: #28a745;
}

.free-shipping-label:hover {
    background-color: #d4edda;
}

.free-price {
    color: #28a745 !important;
    font-weight: bold;
    font-size: 14px;
}

.method-title {
    font-weight: 500;
    color: #333;
}

.method-price {
    font-weight: bold;
    color: #2c5aa0;
}

.per-seller-shipping-summary {
    margin-top: 20px;
    padding: 15px;
    border-top: 2px solid #ddd;
    background-color: #f0f8ff;
}

.shipping-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
}

.shipping-total .label {
    color: #333;
}

.shipping-total .price {
    color: #2c5aa0;
    font-size: 18px;
}

.incomplete-notice {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 3px;
    color: #856404;
    text-align: center;
}

.incomplete-notice .message {
    font-weight: 500;
}
</style>
