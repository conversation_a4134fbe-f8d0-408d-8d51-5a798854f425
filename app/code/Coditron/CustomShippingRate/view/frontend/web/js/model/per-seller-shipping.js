/**
 * Per-Seller Shipping Model
 */
define([
    'ko',
    'underscore',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/shipping-service',
    'Magento_Checkout/js/action/select-shipping-method',
    'mage/translate'
], function (ko, _, quote, shippingService, selectShippingMethodAction, $t) {
    'use strict';

    var perSellerShipping = {
        selectedMethods: ko.observableArray([]),
        totalShippingPrice: ko.observable(0),
        isPerSellerMode: ko.observable(false),
        sellerGroups: ko.observableArray([]),

        /**
         * Initialize per-seller shipping
         */
        initialize: function () {
            var self = this;
            
            // Watch for shipping rates changes
            shippingService.getShippingRates().subscribe(function (rates) {
                self.processShippingRates(rates);
            });

            // Watch for selected methods changes to calculate total
            this.selectedMethods.subscribe(function (methods) {
                self.calculateTotalPrice();
            });

            return this;
        },

        /**
         * Process shipping rates to detect per-seller mode
         */
        processShippingRates: function (rates) {
            var self = this;
            var perSellerRates = [];
            var sellerGroups = {};

            // Check if any rates have seller information
            _.each(rates, function (rate) {
                if (rate.method_code && rate.method_code.indexOf('_seller_') !== -1) {
                    perSellerRates.push(rate);
                    
                    // Extract seller ID from method code
                    var parts = rate.method_code.split('_seller_');
                    var sellerId = parts[1];
                    var serviceType = parts[0];

                    if (!sellerGroups[sellerId]) {
                        sellerGroups[sellerId] = {
                            sellerId: sellerId,
                            sellerName: this.extractSellerName(rate.method_title),
                            methods: [],
                            freeShippingMethods: [],
                            standardShippingMethods: [],
                            hasFreeShipping: false
                        };
                    }

                    var methodData = {
                        code: rate.method_code,
                        title: rate.method_title,
                        price: rate.price_incl_tax || rate.price_excl_tax,
                        serviceType: serviceType,
                        carrier: rate.carrier_code,
                        isFreeOption: rate.method_code.indexOf('free_') === 0
                    };

                    sellerGroups[sellerId].methods.push(methodData);

                    // Separate into free and standard shipping
                    if (methodData.isFreeOption) {
                        sellerGroups[sellerId].freeShippingMethods.push(methodData);
                        sellerGroups[sellerId].hasFreeShipping = true;
                    } else {
                        sellerGroups[sellerId].standardShippingMethods.push(methodData);
                    }
                }
            }, this);

            if (perSellerRates.length > 0) {
                this.isPerSellerMode(true);
                this.sellerGroups(_.values(sellerGroups));
                console.log('Per-seller shipping mode activated', sellerGroups);
            } else {
                this.isPerSellerMode(false);
                this.sellerGroups([]);
            }
        },

        /**
         * Extract seller name from method title
         */
        extractSellerName: function (methodTitle) {
            var match = methodTitle.match(/^([^:]+):/);
            return match ? match[1] : 'Unknown Seller';
        },

        /**
         * Select shipping method for a seller
         */
        selectMethodForSeller: function (sellerId, methodCode) {
            var currentMethods = this.selectedMethods();
            var updatedMethods = [];

            // Remove any existing method for this seller
            _.each(currentMethods, function (method) {
                if (method.sellerId !== sellerId) {
                    updatedMethods.push(method);
                }
            });

            // Add the new method for this seller
            if (methodCode) {
                var selectedMethod = this.findMethodByCode(methodCode);
                if (selectedMethod) {
                    updatedMethods.push({
                        sellerId: sellerId,
                        methodCode: methodCode,
                        price: selectedMethod.price,
                        title: selectedMethod.title
                    });
                }
            }

            this.selectedMethods(updatedMethods);
            this.updateMagentoShippingMethod();
        },

        /**
         * Find method by code
         */
        findMethodByCode: function (methodCode) {
            var foundMethod = null;
            
            _.each(this.sellerGroups(), function (group) {
                _.each(group.methods, function (method) {
                    if (method.code === methodCode) {
                        foundMethod = method;
                        return false; // break
                    }
                });
                if (foundMethod) {
                    return false; // break
                }
            });

            return foundMethod;
        },

        /**
         * Calculate total shipping price
         */
        calculateTotalPrice: function () {
            var total = 0;
            
            _.each(this.selectedMethods(), function (method) {
                total += parseFloat(method.price || 0);
            });

            this.totalShippingPrice(total);
        },

        /**
         * Update Magento's shipping method selection
         */
        updateMagentoShippingMethod: function () {
            var selectedMethods = this.selectedMethods();
            
            if (selectedMethods.length === 0) {
                return;
            }

            // For now, use the first selected method as the primary method
            // In a full implementation, you might need a custom shipping method
            // that handles multiple seller methods
            var primaryMethod = selectedMethods[0];
            var shippingMethod = {
                carrier_code: 'customshippingrate',
                method_code: 'per_seller_combined',
                carrier_title: $t('Per-Seller Shipping'),
                method_title: $t('Combined Shipping from %1 sellers').replace('%1', selectedMethods.length),
                price_excl_tax: this.totalShippingPrice(),
                price_incl_tax: this.totalShippingPrice()
            };

            // Store per-seller method details for backend processing
            shippingMethod.per_seller_methods = selectedMethods;

            selectShippingMethodAction(shippingMethod);
        },

        /**
         * Check if all sellers have selected methods
         */
        isComplete: function () {
            return this.selectedMethods().length === this.sellerGroups().length;
        },

        /**
         * Get selected method for seller
         */
        getSelectedMethodForSeller: function (sellerId) {
            var selectedMethod = _.find(this.selectedMethods(), function (method) {
                return method.sellerId === sellerId;
            });

            return selectedMethod ? selectedMethod.methodCode : null;
        },

        /**
         * Reset selections
         */
        reset: function () {
            this.selectedMethods([]);
            this.totalShippingPrice(0);
        }
    };

    return perSellerShipping.initialize();
});
